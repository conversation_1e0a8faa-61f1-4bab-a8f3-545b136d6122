-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Oct 13, 2025 at 11:02 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.0.30

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `barber_booking`
--
CREATE DATABASE IF NOT EXISTS `barber_booking` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `barber_booking`;

-- --------------------------------------------------------

--
-- Table structure for table `barbers`
--

CREATE TABLE `barbers` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL COMMENT 'ชื่อช่าง',
  `phone` varchar(20) DEFAULT NULL COMMENT 'เบอร์โทรศัพท์',
  `email` varchar(100) DEFAULT NULL COMMENT 'อีเมล',
  `specialties` text DEFAULT NULL COMMENT 'ความเชี่ยวชาญ',
  `image` varchar(255) DEFAULT NULL COMMENT 'รูปภาพช่าง',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'สถานะการทำงาน',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'วันที่เพิ่ม',
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'วันที่แก้ไขล่าสุด'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางช่างตัดผม';

--
-- Dumping data for table `barbers`
--

INSERT INTO `barbers` (`id`, `name`, `phone`, `email`, `specialties`, `image`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'ช่างแดง', '0811111111', '<EMAIL>', 'ตัดผมชาย, จัดแต่งหนวด', NULL, 1, '2025-07-04 06:10:38', '2025-07-04 06:10:38'),
(2, 'ช่างดำ', '0822222222', '<EMAIL>', 'ตัดผมสไตล์โมเดิร์น, ย้อมสี', NULL, 1, '2025-07-04 06:10:38', '2025-07-04 06:10:38'),
(3, 'ช่างขาว', '0833333333', '<EMAIL>', 'ตัดผมคลาสสิค, โกนหนวด', NULL, 1, '2025-07-04 06:10:38', '2025-07-04 06:10:38');

-- --------------------------------------------------------

--
-- Table structure for table `bookings`
--

CREATE TABLE `bookings` (
  `id` int(11) NOT NULL,
  `booking_code` varchar(20) NOT NULL COMMENT 'รหัสการจอง',
  `user_id` int(11) DEFAULT NULL COMMENT 'รหัสผู้ใช้ (NULL สำหรับ walk-in)',
  `service_id` int(11) NOT NULL COMMENT 'รหัสบริการ',
  `barber_id` int(11) NOT NULL COMMENT 'รหัสช่าง',
  `booking_date` date NOT NULL COMMENT 'วันที่จอง',
  `booking_time` time NOT NULL COMMENT 'เวลาที่จอง',
  `customer_name` varchar(100) NOT NULL COMMENT 'ชื่อลูกค้า',
  `customer_phone` varchar(20) NOT NULL COMMENT 'เบอร์โทรลูกค้า',
  `customer_email` varchar(100) DEFAULT NULL COMMENT 'อีเมลลูกค้า',
  `total_price` decimal(10,2) NOT NULL COMMENT 'ราคารวม',
  `status` enum('pending','confirmed','completed','cancelled') DEFAULT 'pending' COMMENT 'สถานะการจอง',
  `notes` text DEFAULT NULL COMMENT 'หมายเหตุ',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'วันที่จอง',
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'วันที่แก้ไขล่าสุด'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางการจอง';

--
-- Dumping data for table `bookings`
--

INSERT INTO `bookings` (`id`, `booking_code`, `user_id`, `service_id`, `barber_id`, `booking_date`, `booking_time`, `customer_name`, `customer_phone`, `customer_email`, `total_price`, `status`, `notes`, `created_at`, `updated_at`) VALUES
(1, 'BK20241219001', NULL, 1, 1, '2024-12-20', '10:00:00', 'สมชาย ใจดี', '0891234567', NULL, 150.00, 'confirmed', NULL, '2025-07-04 06:10:38', '2025-07-04 06:10:38'),
(2, 'BK20241219002', NULL, 2, 2, '2024-12-20', '14:00:00', 'สมหญิง สวยงาม', '0892345678', NULL, 200.00, 'pending', NULL, '2025-07-04 06:10:38', '2025-07-04 06:10:38'),
(3, 'BK20241219003', NULL, 4, 3, '2024-12-21', '11:00:00', 'สมศักดิ์ รวยมาก', '0893456789', NULL, 220.00, 'confirmed', NULL, '2025-07-04 06:10:38', '2025-07-04 06:10:38');

-- --------------------------------------------------------

--
-- Table structure for table `gallery`
--

CREATE TABLE `gallery` (
  `id` int(11) NOT NULL,
  `title` varchar(100) NOT NULL COMMENT 'หัวข้อรูปภาพ',
  `description` text DEFAULT NULL COMMENT 'คำอธิบาย',
  `image` varchar(255) NOT NULL COMMENT 'ชื่อไฟล์รูปภาพ',
  `category` enum('haircut','styling','beard','other') DEFAULT 'other' COMMENT 'หมวดหมู่',
  `is_featured` tinyint(1) DEFAULT 0 COMMENT 'รูปเด่น',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'สถานะการแสดง',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'วันที่เพิ่ม',
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'วันที่แก้ไขล่าสุด'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางแกลเลอรี่';

--
-- Dumping data for table `gallery`
--

INSERT INTO `gallery` (`id`, `title`, `description`, `image`, `category`, `is_featured`, `is_active`, `created_at`, `updated_at`) VALUES
(12, 'การโกนหนวด', 'บริการโกนหนวดแบบมืออาชีพด้วยมีดโกนแบบดั้งเดิม', 'beard_shaving.svg', 'beard', 0, 1, '2025-07-17 06:57:37', '2025-07-17 06:58:26'),
(13, 'จัดแต่งทรงผม', 'การจัดแต่งทรงผมสำหรับงานพิเศษและงานสำคัญ', 'hair_styling.svg', 'styling', 0, 1, '2025-07-17 06:57:37', '2025-07-17 06:58:25'),
(16, 'ื5', 'ทดสอบอัปโหลด', 'test_1752735832_6042.jpg', '', 0, 1, '2025-07-17 07:03:52', '2025-07-17 07:03:52');

-- --------------------------------------------------------

--
-- Table structure for table `promotions`
--

CREATE TABLE `promotions` (
  `id` int(11) NOT NULL,
  `title` varchar(100) NOT NULL COMMENT 'หัวข้อโปรโมชั่น',
  `description` text DEFAULT NULL COMMENT 'รายละเอียด',
  `discount_type` enum('percentage','fixed') NOT NULL COMMENT 'ประเภทส่วนลด',
  `discount_value` decimal(10,2) NOT NULL COMMENT 'มูลค่าส่วนลด',
  `min_amount` decimal(10,2) DEFAULT 0.00 COMMENT 'ยอดขั้นต่ำ',
  `image` varchar(255) DEFAULT NULL COMMENT 'รูปภาพโปรโมชั่น',
  `start_date` date NOT NULL COMMENT 'วันที่เริ่ม',
  `end_date` date NOT NULL COMMENT 'วันที่สิ้นสุด',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'สถานะการใช้งาน',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'วันที่สร้าง',
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'วันที่แก้ไขล่าสุด'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางโปรโมชั่น';

--
-- Dumping data for table `promotions`
--

INSERT INTO `promotions` (`id`, `title`, `description`, `discount_type`, `discount_value`, `min_amount`, `image`, `start_date`, `end_date`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'ลด 20% สำหรับลูกค้าใหม่', 'ลูกค้าใหม่รับส่วนลด 20% สำหรับการตัดผมครั้งแรก', 'percentage', 20.00, 100.00, NULL, '2024-12-01', '2024-12-31', 1, '2025-07-04 06:10:38', '2025-07-04 06:10:38'),
(2, 'ตัดผม 3 ครั้ง ลด 50 บาท', 'จองตัดผม 3 ครั้งรับส่วนลดทันที 50 บาท', 'fixed', 50.00, 400.00, NULL, '2024-12-15', '2025-01-15', 1, '2025-07-04 06:10:38', '2025-07-04 06:10:38');

-- --------------------------------------------------------

--
-- Table structure for table `services`
--

CREATE TABLE `services` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL COMMENT 'ชื่อบริการ',
  `description` text DEFAULT NULL COMMENT 'คำอธิบายบริการ',
  `price` decimal(10,2) NOT NULL COMMENT 'ราคา',
  `duration` int(11) NOT NULL COMMENT 'ระยะเวลา (นาที)',
  `image` varchar(255) DEFAULT NULL COMMENT 'รูปภาพบริการ',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'สถานะการใช้งาน',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'วันที่สร้าง',
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'วันที่แก้ไขล่าสุด'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางบริการ';

--
-- Dumping data for table `services`
--

INSERT INTO `services` (`id`, `name`, `description`, `price`, `duration`, `image`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'ตัดผมชาย', 'ตัดผมสไตล์ชายทั่วไป', 150.00, 30, 'service_1751614350_9785.png', 1, '2025-07-04 06:10:38', '2025-07-04 07:32:30'),
(2, 'ตัดผม + สระ', 'ตัดผมและสระผมรวมกัน', 200.00, 45, 'service_1751612702_6779.jpg', 1, '2025-07-04 06:10:38', '2025-07-04 07:05:02'),
(3, 'โกนหนวด', 'โกนหนวดด้วยมีดโกน', 100.00, 20, 'fix_test_1751613336_8326.jpg', 1, '2025-07-04 06:10:38', '2025-07-04 07:15:36'),
(4, 'ตัดผม + โกนหนวด', 'ตัดผมและโกนหนวดรวมกัน', 220.00, 50, 'service_1751614204_9799.jpg', 1, '2025-07-04 06:10:38', '2025-07-04 07:30:04'),
(5, 'ทดสอบปุ่มบันทึก 13:47:30', 'ทดสอบการทำงานของปุ่มบันทึกการแก้ไข', 54.00, 90, 'service_debug_1751611701_1610.jpg', 1, '2025-07-04 06:10:38', '2025-07-04 06:48:21'),
(6, 'ทดสอบไม่ใช้ CSRF 13:39:07', 'ทดสอบการแก้ไขโดยไม่ใช้ CSRF Token', 399.00, 75, 'service_1751614342_6097.jpg', 1, '2025-07-04 06:10:38', '2025-07-04 07:32:22'),
(7, 'ตัดผมธรรมดา', 'ดาสืิสกดาสทด', 500.00, 6, 'service_1751614397_3579.jpg', 1, '2025-07-04 07:33:01', '2025-07-04 07:33:17');

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(50) NOT NULL COMMENT 'คีย์การตั้งค่า',
  `setting_value` text DEFAULT NULL COMMENT 'ค่าการตั้งค่า',
  `description` varchar(255) DEFAULT NULL COMMENT 'คำอธิบาย',
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'วันที่แก้ไขล่าสุด'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางการตั้งค่าระบบ';

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `description`, `updated_at`) VALUES
(1, 'shop_name', 'ร้านตัดผมสุดหล่อ', 'ชื่อร้าน', '2025-07-04 06:10:38'),
(2, 'shop_phone', '02-123-4567', 'เบอร์โทรร้าน', '2025-07-04 06:10:38'),
(3, 'shop_email', '<EMAIL>', 'อีเมลร้าน', '2025-07-04 06:10:38'),
(4, 'shop_address', '123 ถนนสุขุมวิท กรุงเทพฯ 10110', 'ที่อยู่ร้าน', '2025-07-04 06:10:38'),
(5, 'open_time', '09:00', 'เวลาเปิด', '2025-07-04 06:10:38'),
(6, 'close_time', '19:00', 'เวลาปิด', '2025-07-04 06:10:38'),
(7, 'facebook_url', 'https://www.facebook.com/kittin.singwee.3', 'Facebook', '2025-07-04 06:10:38'),
(8, 'instagram_url', 'https://www.instagram.com/kittin__singwee/', 'Instagram', '2025-07-04 06:10:38'),
(9, 'booking_advance_days', '7', 'จำนวนวันที่สามารถจองล่วงหน้าได้', '2025-07-04 06:10:38'),
(10, 'time_slot_duration', '30', 'ระยะเวลาแต่ละช่วงเวลา (นาที)', '2025-07-04 06:10:38');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `full_name` varchar(100) NOT NULL COMMENT 'ชื่อ-นามสกุล',
  `email` varchar(100) NOT NULL COMMENT 'อีเมล',
  `phone` varchar(20) NOT NULL COMMENT 'เบอร์โทรศัพท์',
  `password` varchar(255) NOT NULL COMMENT 'รหัสผ่าน (hash)',
  `role` enum('admin','customer') DEFAULT 'customer' COMMENT 'บทบาท',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'สถานะการใช้งาน',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'วันที่สร้าง',
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'วันที่แก้ไขล่าสุด'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางผู้ใช้งาน';

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `full_name`, `email`, `phone`, `password`, `role`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'ผู้ดูแลระบบ', '<EMAIL>', '0812345678', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1, '2025-07-04 06:10:38', '2025-07-04 06:10:38'),
(2, 'kittinsing', '<EMAIL>', '1234567890', '$2y$10$EyDOPZU4XV/EsAWDupnOG.CKVRRMhrPnfNRbD2./ieDKMBkrLSEm2', 'customer', 1, '2025-07-10 14:29:53', '2025-07-10 14:29:53');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `barbers`
--
ALTER TABLE `barbers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_barbers_active` (`is_active`);

--
-- Indexes for table `bookings`
--
ALTER TABLE `bookings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `booking_code` (`booking_code`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `service_id` (`service_id`),
  ADD KEY `idx_bookings_date` (`booking_date`),
  ADD KEY `idx_bookings_status` (`status`),
  ADD KEY `idx_bookings_barber` (`barber_id`);

--
-- Indexes for table `gallery`
--
ALTER TABLE `gallery`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `promotions`
--
ALTER TABLE `promotions`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `services`
--
ALTER TABLE `services`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_services_active` (`is_active`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_users_email` (`email`),
  ADD KEY `idx_users_role` (`role`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `barbers`
--
ALTER TABLE `barbers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `bookings`
--
ALTER TABLE `bookings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `gallery`
--
ALTER TABLE `gallery`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `promotions`
--
ALTER TABLE `promotions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `services`
--
ALTER TABLE `services`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `bookings`
--
ALTER TABLE `bookings`
  ADD CONSTRAINT `bookings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `bookings_ibfk_2` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`),
  ADD CONSTRAINT `bookings_ibfk_3` FOREIGN KEY (`barber_id`) REFERENCES `barbers` (`id`);
--
-- Database: `dbemp`
--
CREATE DATABASE IF NOT EXISTS `dbemp` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE `dbemp`;

-- --------------------------------------------------------

--
-- Table structure for table `department`
--

CREATE TABLE `department` (
  `DepartmentID` varchar(2) NOT NULL,
  `DepartmentName` varchar(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `department`
--

INSERT INTO `department` (`DepartmentID`, `DepartmentName`) VALUES
('AC', 'บัญชี'),
('BA', 'บริหาร'),
('HR', 'งานบุคคล'),
('MK', 'การตลาด'),
('PG', 'โปรแกรมเมอร์'),
('PR', 'ประชาสัมพันธ์'),
('SE', 'ส่งเสริมการขาย');

-- --------------------------------------------------------

--
-- Table structure for table `employee`
--

CREATE TABLE `employee` (
  `EmployeeID` varchar(6) NOT NULL,
  `Title` varchar(10) NOT NULL,
  `Name` varchar(30) NOT NULL,
  `Sex` varchar(10) NOT NULL,
  `Education` varchar(20) NOT NULL,
  `Start_Date` date NOT NULL,
  `Salary` float NOT NULL,
  `DepartmentID` varchar(2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `employee`
--

INSERT INTO `employee` (`EmployeeID`, `Title`, `Name`, `Sex`, `Education`, `Start_Date`, `Salary`, `DepartmentID`) VALUES
('1', 'นาย', 'กฤษติณห์', 'ชาย', 'ปริญญาตรี', '2026-01-01', 30000, 'AC'),
('405039', 'นาย', 'ถิรวัฒน์', 'ชาย', 'ปริญญาตรี', '2016-10-14', 14000, 'HR'),
('405054', 'นาย', 'นิติ', 'ชาย', 'ปริญญาโท', '2017-04-10', 25600, 'BA'),
('405088', 'นาย', 'ทศพล', 'ชาย', 'ปริญญาตรี', '2016-10-06', 13600, 'SE'),
('405096', 'นาย', 'สมเกียรติ', 'ชาย', 'ปริญญาตรี', '2016-08-06', 14200, 'HR'),
('405112', 'นาย', 'ชลายุทธ', 'ชาย', 'ปริญญาตรี', '2017-09-30', 12800, 'SE'),
('405138', 'นาย', 'สุทธิพงษ์', 'ชาย', 'ปริญญาตรี', '2018-05-09', 18600, 'PG'),
('405204', 'นาย', 'ปรัชญา', 'ชาย', 'ปริญญาตรี', '2016-10-06', 15700, 'AC'),
('405211', 'นาย', 'รณเทพ', 'ชาย', 'ปริญญาตรี', '2016-08-06', 24000, 'BA'),
('405245', 'นาย', 'อภิศักดิ์', 'ชาย', 'ปริญญาตรี', '2018-02-06', 17900, 'PG');

-- --------------------------------------------------------

--
-- Stand-in structure for view `emp_view`
-- (See below for the actual view)
--
CREATE TABLE `emp_view` (
`EmployeeID` varchar(6)
,`Title` varchar(10)
,`Name` varchar(30)
,`Start_Date` date
);

-- --------------------------------------------------------

--
-- Structure for view `emp_view`
--
DROP TABLE IF EXISTS `emp_view`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `emp_view`  AS SELECT `employee`.`EmployeeID` AS `EmployeeID`, `employee`.`Title` AS `Title`, `employee`.`Name` AS `Name`, `employee`.`Start_Date` AS `Start_Date` FROM `employee` WHERE `employee`.`Salary` >= 15000 ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `department`
--
ALTER TABLE `department`
  ADD PRIMARY KEY (`DepartmentID`);

--
-- Indexes for table `employee`
--
ALTER TABLE `employee`
  ADD PRIMARY KEY (`EmployeeID`),
  ADD KEY `DepartmentID` (`DepartmentID`);

--
-- Constraints for dumped tables
--

--
-- Constraints for table `employee`
--
ALTER TABLE `employee`
  ADD CONSTRAINT `employee_ibfk_1` FOREIGN KEY (`DepartmentID`) REFERENCES `department` (`DepartmentID`);
--
-- Database: `phpmyadmin`
--
CREATE DATABASE IF NOT EXISTS `phpmyadmin` DEFAULT CHARACTER SET utf8 COLLATE utf8_bin;
USE `phpmyadmin`;

-- --------------------------------------------------------

--
-- Table structure for table `pma__bookmark`
--

CREATE TABLE `pma__bookmark` (
  `id` int(10) UNSIGNED NOT NULL,
  `dbase` varchar(255) NOT NULL DEFAULT '',
  `user` varchar(255) NOT NULL DEFAULT '',
  `label` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `query` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Bookmarks';

-- --------------------------------------------------------

--
-- Table structure for table `pma__central_columns`
--

CREATE TABLE `pma__central_columns` (
  `db_name` varchar(64) NOT NULL,
  `col_name` varchar(64) NOT NULL,
  `col_type` varchar(64) NOT NULL,
  `col_length` text DEFAULT NULL,
  `col_collation` varchar(64) NOT NULL,
  `col_isNull` tinyint(1) NOT NULL,
  `col_extra` varchar(255) DEFAULT '',
  `col_default` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Central list of columns';

-- --------------------------------------------------------

--
-- Table structure for table `pma__column_info`
--

CREATE TABLE `pma__column_info` (
  `id` int(5) UNSIGNED NOT NULL,
  `db_name` varchar(64) NOT NULL DEFAULT '',
  `table_name` varchar(64) NOT NULL DEFAULT '',
  `column_name` varchar(64) NOT NULL DEFAULT '',
  `comment` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `mimetype` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `transformation` varchar(255) NOT NULL DEFAULT '',
  `transformation_options` varchar(255) NOT NULL DEFAULT '',
  `input_transformation` varchar(255) NOT NULL DEFAULT '',
  `input_transformation_options` varchar(255) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Column information for phpMyAdmin';

-- --------------------------------------------------------

--
-- Table structure for table `pma__designer_settings`
--

CREATE TABLE `pma__designer_settings` (
  `username` varchar(64) NOT NULL,
  `settings_data` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Settings related to Designer';

--
-- Dumping data for table `pma__designer_settings`
--

INSERT INTO `pma__designer_settings` (`username`, `settings_data`) VALUES
('root', '{\"snap_to_grid\":\"off\",\"relation_lines\":\"true\",\"angular_direct\":\"direct\"}');

-- --------------------------------------------------------

--
-- Table structure for table `pma__export_templates`
--

CREATE TABLE `pma__export_templates` (
  `id` int(5) UNSIGNED NOT NULL,
  `username` varchar(64) NOT NULL,
  `export_type` varchar(10) NOT NULL,
  `template_name` varchar(64) NOT NULL,
  `template_data` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Saved export templates';

-- --------------------------------------------------------

--
-- Table structure for table `pma__favorite`
--

CREATE TABLE `pma__favorite` (
  `username` varchar(64) NOT NULL,
  `tables` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Favorite tables';

-- --------------------------------------------------------

--
-- Table structure for table `pma__history`
--

CREATE TABLE `pma__history` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `username` varchar(64) NOT NULL DEFAULT '',
  `db` varchar(64) NOT NULL DEFAULT '',
  `table` varchar(64) NOT NULL DEFAULT '',
  `timevalue` timestamp NOT NULL DEFAULT current_timestamp(),
  `sqlquery` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='SQL history for phpMyAdmin';

-- --------------------------------------------------------

--
-- Table structure for table `pma__navigationhiding`
--

CREATE TABLE `pma__navigationhiding` (
  `username` varchar(64) NOT NULL,
  `item_name` varchar(64) NOT NULL,
  `item_type` varchar(64) NOT NULL,
  `db_name` varchar(64) NOT NULL,
  `table_name` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Hidden items of navigation tree';

-- --------------------------------------------------------

--
-- Table structure for table `pma__pdf_pages`
--

CREATE TABLE `pma__pdf_pages` (
  `db_name` varchar(64) NOT NULL DEFAULT '',
  `page_nr` int(10) UNSIGNED NOT NULL,
  `page_descr` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='PDF relation pages for phpMyAdmin';

-- --------------------------------------------------------

--
-- Table structure for table `pma__recent`
--

CREATE TABLE `pma__recent` (
  `username` varchar(64) NOT NULL,
  `tables` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Recently accessed tables';

--
-- Dumping data for table `pma__recent`
--

INSERT INTO `pma__recent` (`username`, `tables`) VALUES
('root', '[{\"db\":\"dbemp\",\"table\":\"emp_view\"},{\"db\":\"dbemp\",\"table\":\"employee\"},{\"db\":\"dbemp\",\"table\":\"department\"},{\"db\":\"barber_booking\",\"table\":\"gallery\"},{\"db\":\"barber_booking\",\"table\":\"services\"},{\"db\":\"barber_booking\",\"table\":\"settings\"},{\"db\":\"barber_booking\",\"table\":\"users\"},{\"db\":\"barber_booking\",\"table\":\"barbers\"},{\"db\":\"barber_booking\",\"table\":\"bookings\"},{\"db\":\"barber_booking\",\"table\":\"promotions\"}]');

-- --------------------------------------------------------

--
-- Table structure for table `pma__relation`
--

CREATE TABLE `pma__relation` (
  `master_db` varchar(64) NOT NULL DEFAULT '',
  `master_table` varchar(64) NOT NULL DEFAULT '',
  `master_field` varchar(64) NOT NULL DEFAULT '',
  `foreign_db` varchar(64) NOT NULL DEFAULT '',
  `foreign_table` varchar(64) NOT NULL DEFAULT '',
  `foreign_field` varchar(64) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Relation table';

-- --------------------------------------------------------

--
-- Table structure for table `pma__savedsearches`
--

CREATE TABLE `pma__savedsearches` (
  `id` int(5) UNSIGNED NOT NULL,
  `username` varchar(64) NOT NULL DEFAULT '',
  `db_name` varchar(64) NOT NULL DEFAULT '',
  `search_name` varchar(64) NOT NULL DEFAULT '',
  `search_data` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Saved searches';

-- --------------------------------------------------------

--
-- Table structure for table `pma__table_coords`
--

CREATE TABLE `pma__table_coords` (
  `db_name` varchar(64) NOT NULL DEFAULT '',
  `table_name` varchar(64) NOT NULL DEFAULT '',
  `pdf_page_number` int(11) NOT NULL DEFAULT 0,
  `x` float UNSIGNED NOT NULL DEFAULT 0,
  `y` float UNSIGNED NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Table coordinates for phpMyAdmin PDF output';

-- --------------------------------------------------------

--
-- Table structure for table `pma__table_info`
--

CREATE TABLE `pma__table_info` (
  `db_name` varchar(64) NOT NULL DEFAULT '',
  `table_name` varchar(64) NOT NULL DEFAULT '',
  `display_field` varchar(64) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Table information for phpMyAdmin';

--
-- Dumping data for table `pma__table_info`
--

INSERT INTO `pma__table_info` (`db_name`, `table_name`, `display_field`) VALUES
('dbemp', 'employee', 'EmployeeID');

-- --------------------------------------------------------

--
-- Table structure for table `pma__table_uiprefs`
--

CREATE TABLE `pma__table_uiprefs` (
  `username` varchar(64) NOT NULL,
  `db_name` varchar(64) NOT NULL,
  `table_name` varchar(64) NOT NULL,
  `prefs` text NOT NULL,
  `last_update` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Tables'' UI preferences';

-- --------------------------------------------------------

--
-- Table structure for table `pma__tracking`
--

CREATE TABLE `pma__tracking` (
  `db_name` varchar(64) NOT NULL,
  `table_name` varchar(64) NOT NULL,
  `version` int(10) UNSIGNED NOT NULL,
  `date_created` datetime NOT NULL,
  `date_updated` datetime NOT NULL,
  `schema_snapshot` text NOT NULL,
  `schema_sql` text DEFAULT NULL,
  `data_sql` longtext DEFAULT NULL,
  `tracking` set('UPDATE','REPLACE','INSERT','DELETE','TRUNCATE','CREATE DATABASE','ALTER DATABASE','DROP DATABASE','CREATE TABLE','ALTER TABLE','RENAME TABLE','DROP TABLE','CREATE INDEX','DROP INDEX','CREATE VIEW','ALTER VIEW','DROP VIEW') DEFAULT NULL,
  `tracking_active` int(1) UNSIGNED NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Database changes tracking for phpMyAdmin';

-- --------------------------------------------------------

--
-- Table structure for table `pma__userconfig`
--

CREATE TABLE `pma__userconfig` (
  `username` varchar(64) NOT NULL,
  `timevalue` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `config_data` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='User preferences storage for phpMyAdmin';

--
-- Dumping data for table `pma__userconfig`
--

INSERT INTO `pma__userconfig` (`username`, `timevalue`, `config_data`) VALUES
('root', '2025-10-13 08:19:26', '{\"Console\\/Mode\":\"collapse\",\"NavigationWidth\":231}');

-- --------------------------------------------------------

--
-- Table structure for table `pma__usergroups`
--

CREATE TABLE `pma__usergroups` (
  `usergroup` varchar(64) NOT NULL,
  `tab` varchar(64) NOT NULL,
  `allowed` enum('Y','N') NOT NULL DEFAULT 'N'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='User groups with configured menu items';

-- --------------------------------------------------------

--
-- Table structure for table `pma__users`
--

CREATE TABLE `pma__users` (
  `username` varchar(64) NOT NULL,
  `usergroup` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='Users and their assignments to user groups';

--
-- Indexes for dumped tables
--

--
-- Indexes for table `pma__bookmark`
--
ALTER TABLE `pma__bookmark`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `pma__central_columns`
--
ALTER TABLE `pma__central_columns`
  ADD PRIMARY KEY (`db_name`,`col_name`);

--
-- Indexes for table `pma__column_info`
--
ALTER TABLE `pma__column_info`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `db_name` (`db_name`,`table_name`,`column_name`);

--
-- Indexes for table `pma__designer_settings`
--
ALTER TABLE `pma__designer_settings`
  ADD PRIMARY KEY (`username`);

--
-- Indexes for table `pma__export_templates`
--
ALTER TABLE `pma__export_templates`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `u_user_type_template` (`username`,`export_type`,`template_name`);

--
-- Indexes for table `pma__favorite`
--
ALTER TABLE `pma__favorite`
  ADD PRIMARY KEY (`username`);

--
-- Indexes for table `pma__history`
--
ALTER TABLE `pma__history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `username` (`username`,`db`,`table`,`timevalue`);

--
-- Indexes for table `pma__navigationhiding`
--
ALTER TABLE `pma__navigationhiding`
  ADD PRIMARY KEY (`username`,`item_name`,`item_type`,`db_name`,`table_name`);

--
-- Indexes for table `pma__pdf_pages`
--
ALTER TABLE `pma__pdf_pages`
  ADD PRIMARY KEY (`page_nr`),
  ADD KEY `db_name` (`db_name`);

--
-- Indexes for table `pma__recent`
--
ALTER TABLE `pma__recent`
  ADD PRIMARY KEY (`username`);

--
-- Indexes for table `pma__relation`
--
ALTER TABLE `pma__relation`
  ADD PRIMARY KEY (`master_db`,`master_table`,`master_field`),
  ADD KEY `foreign_field` (`foreign_db`,`foreign_table`);

--
-- Indexes for table `pma__savedsearches`
--
ALTER TABLE `pma__savedsearches`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `u_savedsearches_username_dbname` (`username`,`db_name`,`search_name`);

--
-- Indexes for table `pma__table_coords`
--
ALTER TABLE `pma__table_coords`
  ADD PRIMARY KEY (`db_name`,`table_name`,`pdf_page_number`);

--
-- Indexes for table `pma__table_info`
--
ALTER TABLE `pma__table_info`
  ADD PRIMARY KEY (`db_name`,`table_name`);

--
-- Indexes for table `pma__table_uiprefs`
--
ALTER TABLE `pma__table_uiprefs`
  ADD PRIMARY KEY (`username`,`db_name`,`table_name`);

--
-- Indexes for table `pma__tracking`
--
ALTER TABLE `pma__tracking`
  ADD PRIMARY KEY (`db_name`,`table_name`,`version`);

--
-- Indexes for table `pma__userconfig`
--
ALTER TABLE `pma__userconfig`
  ADD PRIMARY KEY (`username`);

--
-- Indexes for table `pma__usergroups`
--
ALTER TABLE `pma__usergroups`
  ADD PRIMARY KEY (`usergroup`,`tab`,`allowed`);

--
-- Indexes for table `pma__users`
--
ALTER TABLE `pma__users`
  ADD PRIMARY KEY (`username`,`usergroup`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `pma__bookmark`
--
ALTER TABLE `pma__bookmark`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pma__column_info`
--
ALTER TABLE `pma__column_info`
  MODIFY `id` int(5) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pma__export_templates`
--
ALTER TABLE `pma__export_templates`
  MODIFY `id` int(5) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pma__history`
--
ALTER TABLE `pma__history`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pma__pdf_pages`
--
ALTER TABLE `pma__pdf_pages`
  MODIFY `page_nr` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pma__savedsearches`
--
ALTER TABLE `pma__savedsearches`
  MODIFY `id` int(5) UNSIGNED NOT NULL AUTO_INCREMENT;
--
-- Database: `test`
--
CREATE DATABASE IF NOT EXISTS `test` DEFAULT CHARACTER SET latin1 COLLATE latin1_swedish_ci;
USE `test`;
--
-- Database: `upload_db`
--
CREATE DATABASE IF NOT EXISTS `upload_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE `upload_db`;

-- --------------------------------------------------------

--
-- Table structure for table `images`
--

CREATE TABLE `images` (
  `id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `uploaded_on` datetime NOT NULL,
  `status` enum(' 1',' 0') NOT NULL DEFAULT ' 1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `images`
--

INSERT INTO `images` (`id`, `file_name`, `uploaded_on`, `status`) VALUES
(1, 'banner-bg.png', '2025-07-04 11:53:44', ' 1'),
(2, 'package-3.png', '2025-07-04 11:55:05', ' 1');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `images`
--
ALTER TABLE `images`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `images`
--
ALTER TABLE `images`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
