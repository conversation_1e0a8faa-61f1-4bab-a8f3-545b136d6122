<?php
/**
 * ไฟล์การตั้งค่าฐานข้อมูล
 * Database Configuration File
 */

// การตั้งค่าฐานข้อมูล
define('DB_HOST', 'localhost');
define('DB_PORT', '3306');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_BOOKSTORE', 'bookstoredb');
define('DB_NAME', DB_BOOKSTORE);
define('DB_CHARSET', 'utf8mb4');

/**
 * คลาสจัดการฐานข้อมูล
 */
class Database {
    private static $connections = [];
    
    /**
     * สร้างการเชื่อมต่อฐานข้อมูล
     */
    public static function connect($dbname) {
        if (!isset(self::$connections[$dbname])) {
            try {
                $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . $dbname . ";charset=" . DB_CHARSET;
                $pdo = new PDO($dsn, DB_USERNAME, DB_PASSWORD);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
                self::$connections[$dbname] = $pdo;
            } catch (PDOException $e) {
                throw new Exception("การเชื่อมต่อฐานข้อมูล $dbname ล้มเหลว: " . $e->getMessage());
            }
        }
        return self::$connections[$dbname];
    }
    
    /**
     * ปิดการเชื่อมต่อทั้งหมด
     */
    public static function closeAll() {
        self::$connections = [];
    }
}

/**
 * ฟังก์ชันช่วยเหลือ
 */

// เชื่อมต่อฐานข้อมูลทั่วไป
function getDB($dbname = DB_NAME) {
    return Database::connect($dbname);
}

// เชื่อมต่อฐานข้อมูลร้านหนังสือ
function getBookstoreDB() {
    return Database::connect(DB_BOOKSTORE);
}

// ทดสอบการเชื่อมต่อ
function testConnection($dbname = DB_NAME) {
    try {
        $pdo = getDB($dbname);
        return [
            'success' => true,
            'message' => "เชื่อมต่อฐานข้อมูล $dbname สำเร็จ",
            'server_info' => $pdo->getAttribute(PDO::ATTR_SERVER_VERSION)
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

// แสดงข้อมูลการเชื่อมต่อ
function showConnectionInfo() {
    return [
        'host' => DB_HOST,
        'port' => DB_PORT,
        'username' => DB_USERNAME,
        'database' => DB_NAME,
        'charset' => DB_CHARSET
    ];
}
?>
