<?php
/**
 * ไฟล์การตั้งค่าการเชื่อมต่อฐานข้อมูล
 * Database Configuration File
 */

// การตั้งค่าฐานข้อมูลหลัก (Main Database Configuration)
define('DB_HOST', 'localhost');
define('DB_PORT', '3306');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');

// ฐานข้อมูลระบบพนักงาน (Employee Database)
define('DB_EMPLOYEE', 'dbemp');           // ระบบพนักงาน

// ฐานข้อมูลเริ่มต้น (Default Database)
define('DB_NAME', DB_EMPLOYEE);

// การตั้งค่า PDO
define('DB_CHARSET', 'utf8mb4');
define('DB_OPTIONS', [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
]);

/**
 * คลาสสำหรับการเชื่อมต่อฐานข้อมูล
 * Database Connection Class
 */
class Database {
    private static $connections = [];
    
    /**
     * เชื่อมต่อฐานข้อมูลด้วย PDO
     * @param string $database ชื่อฐานข้อมูล
     * @return PDO
     */
    public static function connect($database = DB_NAME) {
        // ตรวจสอบว่ามีการเชื่อมต่อแล้วหรือไม่
        if (isset(self::$connections[$database])) {
            return self::$connections[$database];
        }
        
        try {
            $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . $database . ";charset=" . DB_CHARSET;
            $pdo = new PDO($dsn, DB_USERNAME, DB_PASSWORD, DB_OPTIONS);
            
            // เก็บการเชื่อมต่อไว้ใน array
            self::$connections[$database] = $pdo;
            
            return $pdo;
        } catch (PDOException $e) {
            throw new PDOException("การเชื่อมต่อฐานข้อมูลล้มเหลว: " . $e->getMessage());
        }
    }
    
    /**
     * เชื่อมต่อฐานข้อมูลระบบพนักงาน
     * @return PDO
     */
    public static function connectEmployee() {
        return self::connect(DB_EMPLOYEE);
    }
    
    /**
     * ปิดการเชื่อมต่อฐานข้อมูลทั้งหมด
     */
    public static function closeAll() {
        self::$connections = [];
    }
    
    /**
     * ปิดการเชื่อมต่อฐานข้อมูลเฉพาะ
     * @param string $database ชื่อฐานข้อมูล
     */
    public static function close($database) {
        if (isset(self::$connections[$database])) {
            unset(self::$connections[$database]);
        }
    }
}

/**
 * ฟังก์ชันช่วยเหลือสำหรับการเชื่อมต่อฐานข้อมูล
 * Helper Functions for Database Connection
 */

/**
 * ฟังก์ชันเชื่อมต่อฐานข้อมูลแบบง่าย
 * @param string $database ชื่อฐานข้อมูล
 * @return PDO
 */
function getDB($database = DB_NAME) {
    return Database::connect($database);
}

/**
 * ฟังก์ชันเชื่อมต่อฐานข้อมูลระบบพนักงาน
 * @return PDO
 */
function getEmployeeDB() {
    return Database::connectEmployee();
}

/**
 * ฟังก์ชันทดสอบการเชื่อมต่อฐานข้อมูล
 * @param string $database ชื่อฐานข้อมูล
 * @return bool
 */
function testConnection($database = DB_NAME) {
    try {
        $pdo = Database::connect($database);
        $stmt = $pdo->query("SELECT 1");
        return $stmt !== false;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * ฟังก์ชันแสดงข้อมูลการเชื่อมต่อ
 */
function showConnectionInfo() {
    echo "<h3>ข้อมูลการเชื่อมต่อฐานข้อมูล</h3>";
    echo "<p><strong>Host:</strong> " . DB_HOST . ":" . DB_PORT . "</p>";
    echo "<p><strong>Username:</strong> " . DB_USERNAME . "</p>";
    echo "<p><strong>Database:</strong> " . DB_NAME . " (ระบบพนักงาน)</p>";
    echo "<p><strong>Charset:</strong> " . DB_CHARSET . "</p>";

    echo "<h4>สถานะการเชื่อมต่อ:</h4>";
    echo "<ul>";
    echo "<li>Employee DB: " . (testConnection(DB_EMPLOYEE) ? "✅ เชื่อมต่อสำเร็จ" : "❌ เชื่อมต่อไม่สำเร็จ") . "</li>";
    echo "</ul>";
}
?>
